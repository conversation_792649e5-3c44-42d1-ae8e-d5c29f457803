import 'package:dandanplay_flutter/model/file_item.dart';
import 'package:dandanplay_flutter/service/file_explorer.dart';
import 'package:dandanplay_flutter/theme/tile_group_style.dart';
import 'package:dandanplay_flutter/utils/format.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:signals_flutter/signals_flutter.dart';

class EpisodePanel extends StatelessWidget {
  final void Function(String, int) onEpisodeSelected;

  EpisodePanel({super.key, required this.onEpisodeSelected});

  final FileExplorerService fileExplorerService =
      GetIt.I.get<FileExplorerService>();
  List<FTileMixin> _buildTiles(List<FileItem> files, BuildContext context) {
    final widgetList = <FTileMixin>[];
    for (var i = 0; i < files.length; i++) {
      final file = files[i];
      if (!file.isVideo) {
        continue;
      }
      widgetList.add(
        FTile(
          style: videoTileStyle(
            colors: context.theme.colors,
            typography: context.theme.typography,
            style: context.theme.style,
            newColors: context.theme.colors,
          ),
          title: Text(file.name, maxLines: 2),
          subtitle:
              file.history != null
                  ? Text(
                    '观看进度: ${formatTime(file.history!.position, file.history!.duration)}',
                  )
                  : Text(''),
          onPress:
              () => {
                onEpisodeSelected(fileExplorerService.rootPath! + file.path, i),
                Navigator.pop(context),
              },
        ),
      );
    }
    return widgetList;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(color: context.theme.colors.background),
      width: 300,
      height: MediaQuery.of(context).size.height,
      child: Scaffold(
        body: Watch(
          (context) => fileExplorerService.files.value.map(
            data: (files) {
              if (files.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        FIcons.folder,
                        size: 48,
                        color: context.theme.colors.mutedForeground,
                      ),
                      const SizedBox(height: 16),
                      Text('播放列表为空', style: context.theme.typography.lg),
                    ],
                  ),
                );
              }
              return FTileGroup(
                divider: FTileDivider.indented,
                style: tileGroupStyle(
                  colors: context.theme.colors,
                  typography: context.theme.typography,
                  style: context.theme.style,
                  newColors: context.theme.colors.copyWith(
                    border: const Color.fromARGB(0, 238, 238, 238),
                  ),
                ),
                children: _buildTiles(files, context),
              );
            },
            error: (error, stack) => const Center(child: Text('加载失败')),
            loading: () => const Center(child: CircularProgressIndicator()),
          ),
        ),
      ),
    );
  }
}
