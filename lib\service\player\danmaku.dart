import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:canvas_danmaku/danmaku_controller.dart';
import 'package:canvas_danmaku/models/danmaku_option.dart';
import 'package:dandanplay_flutter/model/danmaku.dart';
import 'package:dandanplay_flutter/model/danmaku_match.dart';
import 'package:dandanplay_flutter/service/history.dart';
import 'package:dandanplay_flutter/service/player/danmaku_optimizer.dart';
import 'package:dandanplay_flutter/service/storage.dart';
import 'package:dandanplay_flutter/utils/crypto_utils.dart';
import 'package:dandanplay_flutter/utils/danmaku_api_utils.dart';
import 'package:dandanplay_flutter/page/player/notification.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:signals_flutter/signals_flutter.dart';

class DanmakuService {
  // TODO
  // 1. 弹幕播放状态切换
  // 2. 弹幕配置更新
  // 3. 弹幕加载（add）
  DanmakuController controller = DanmakuController(
    onAddDanmaku: (danmaku) {},
    onUpdateOption: (option) {},
    onPause: () {},
    onResume: () {},
    onClear: () {},
  );
  // DanmakuOption(
  //         fontSize: 16.0 * settings.fontSizeScale,
  //         opacity: settings.opacity,
  //         fontWeight: settings.fontWeight.index,
  //         showStroke: true,
  //         area: settings.density,
  //         duration: (8 / settings.speedScale).round(),
  //       )
  DanmakuOption option = DanmakuOption(
    fontSize: 16.0,
    opacity: 0.8,
    fontWeight: 4,
    showStroke: true,
    area: 1.0,
    duration: 8,
  );
  // 弹幕匹配状态信号
  final Signal<DanmakuMatchStatus> _danmakuMatchStatus = Signal(
    DanmakuMatchStatus.idle,
  );
  final Signal<DanmakuMatchResult?> _danmakuMatchResult = Signal(null);
  // 弹幕相关信号
  final Signal<List<Danmaku>> _danmakus = Signal(<Danmaku>[]);
  final Signal<DanmakuSettings> _danmakuSettings = Signal(
    const DanmakuSettings(),
  ); // TODO 导向Configure
  late String currentVideoPath;
  late History history;
  late Duration duration;

  /// 弹幕列表
  ReadonlySignal<List<Danmaku>> get danmakus => _danmakus.readonly();

  /// 弹幕设置
  ReadonlySignal<DanmakuSettings> get danmakuSettings =>
      _danmakuSettings.readonly();

  /// 弹幕匹配状态
  ReadonlySignal<DanmakuMatchStatus> get danmakuMatchStatus =>
      _danmakuMatchStatus.readonly();

  /// 弹幕匹配结果
  ReadonlySignal<DanmakuMatchResult?> get danmakuMatchResult =>
      _danmakuMatchResult.readonly();

  void syncWithVideo(bool isPlaying) {
    if (isPlaying) {
      controller.resume();
    } else {
      controller.pause();
    }
  }

  /// 替换本地弹幕文件
  Future<void> replaceLocalDanmaku() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xml', 'json'],
      );

      if (result != null && result.files.single.path != null) {
        final filePath = result.files.single.path!;
        PlayerNotificationManager.showInfo('正在加载本地弹幕...');

        // 调用服务层来处理文件加载和解析
        final success = await loadDanmakuFromFile(filePath);
        if (success) {
          PlayerNotificationManager.showSuccess('本地弹幕加载成功');
          // 弹幕会自动更新，因为UI监听了danmakus信号
        } else {
          PlayerNotificationManager.showError('弹幕文件格式无效');
        }
      } else {
        // User canceled the picker
      }
    } catch (e) {
      debugPrint('选择或加载本地弹幕失败: $e');
      PlayerNotificationManager.showError('加载本地弹幕失败: $e');
    }
  }

  /// 从本地文件加载弹幕
  Future<bool> loadDanmakuFromFile(String filePath) async {
    // TDOD 支持xml按照dandanplay样式
    if (!filePath.toLowerCase().endsWith('.json')) {
      debugPrint('不支持的弹幕文件格式，仅支持 .json');
      return false;
    }

    try {
      final file = File(filePath);
      if (!await file.exists()) {
        debugPrint('弹幕文件不存在: $filePath');
        return false;
      }

      // 读取并解析弹幕文件 (逻辑与 _getCachedDanmakus 类似)
      final jsonString = await file.readAsString();
      final jsonData = jsonDecode(jsonString) as List;
      final newDanmakus =
          jsonData
              .map((item) => Danmaku.fromJson(item as Map<String, dynamic>))
              .toList();

      // 更新弹幕列表
      _danmakus.value = newDanmakus;
      debugPrint('从本地文件加载弹幕成功: ${newDanmakus.length}条');

      // 将这个“本地”弹幕保存到缓存，以便统一管理
      await _saveDanmakusToCache(currentVideoPath, newDanmakus);

      return true;
    } catch (e) {
      debugPrint('从本地文件加载弹幕失败: $e');
      // 失败时清空弹幕
      _danmakus.value = [];
      return false;
    }
  }

  /// 加载弹幕
  Future<void> loadDanmaku({bool force = false}) async {
    try {
      // 1. 检查本地缓存 (非强制刷新时)
      if (!force) {
        final cachedDanmakus = await _getCachedDanmakus(currentVideoPath);
        if (cachedDanmakus.isNotEmpty) {
          _danmakus.value = cachedDanmakus;
          debugPrint('从缓存加载弹幕: ${cachedDanmakus.length}条');
          return;
        }
      }

      // 2. 如果没有缓存或强制刷新，尝试从弹弹play API获取（带重试机制）
      final danmakus = await _fetchDanmakusWithRetry(currentVideoPath);

      // 3. 保存到本地缓存
      if (danmakus.isNotEmpty) {
        await _saveDanmakusToCache(currentVideoPath, danmakus);
      }

      // 4. 更新弹幕列表
      _danmakus.value = danmakus;
      debugPrint('从API加载弹幕: ${danmakus.length}条');
    } catch (e) {
      debugPrint('加载弹幕失败: $e');
      // 加载失败时设置空列表，避免界面异常
      _danmakus.value = [];
    }
  }

  /// 从缓存获取弹幕数据（优化版本）
  /// 支持新的缓存格式，包含过期时间检查
  Future<List<Danmaku>> _getCachedDanmakus(String videoPath) async {
    try {
      final uniqueKey = CryptoUtils.generateVideoUniqueKey(videoPath);
      final cacheDir = Directory('${Directory.systemTemp.path}/danmaku');
      final danmakuFile = File('${cacheDir.path}/$uniqueKey.json');

      if (!await danmakuFile.exists()) {
        return [];
      }

      // 读取并解析弹幕文件
      final jsonString = await danmakuFile.readAsString();
      final cacheData = jsonDecode(jsonString) as Map<String, dynamic>;

      // 检查缓存版本
      final version = cacheData['version'] as String?;
      if (version == '2.0') {
        // 新版本缓存格式，检查过期时间
        final expireTime = cacheData['expireTime'] as int?;
        if (expireTime != null) {
          final now = DateTime.now().millisecondsSinceEpoch;
          if (now > expireTime) {
            debugPrint('弹幕缓存已过期，删除缓存文件');
            await danmakuFile.delete();
            return [];
          }
        }

        // 解析弹幕数据
        final danmakusJson = cacheData['danmakus'] as List;
        return danmakusJson
            .map((item) => Danmaku.fromJson(item as Map<String, dynamic>))
            .toList();
      } else {
        // 旧版本缓存格式，兼容处理
        debugPrint('发现旧版本缓存格式，删除并重新加载');
        await danmakuFile.delete();
        return [];
      }
    } catch (e) {
      debugPrint('读取缓存弹幕失败: $e');
      return [];
    }
  }

  /// 带重试机制的弹幕获取
  Future<List<Danmaku>> _fetchDanmakusWithRetry(String videoPath) async {
    const maxRetries = 3;
    const retryDelay = Duration(seconds: 2);

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        debugPrint('尝试获取弹幕，第$attempt次');
        return await _fetchDanmakusFromApi(videoPath);
      } catch (e) {
        debugPrint('第$attempt次获取弹幕失败: $e');

        // 检查是否为网络错误
        if (_isNetworkError(e)) {
          debugPrint('检测到网络错误，将重试');
        } else {
          debugPrint('非网络错误，停止重试');
          break; // 非网络错误，不重试
        }

        if (attempt == maxRetries) {
          rethrow; // 最后一次尝试失败，抛出异常
        }

        // 等待后重试
        await Future.delayed(retryDelay);
      }
    }

    return []; // 重试失败或非网络错误
  }

  /// 检查是否为网络错误
  bool _isNetworkError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('network') ||
        errorString.contains('connection') ||
        errorString.contains('timeout') ||
        errorString.contains('socket') ||
        errorString.contains('dns') ||
        errorString.contains('unreachable');
  }

  /// 从弹弹play API获取弹幕数据
  Future<List<Danmaku>> _fetchDanmakusFromApi(String videoPath) async {
    final startTime = DateTime.now();
    final isNetworkVideo =
        videoPath.startsWith('http://') || videoPath.startsWith('https://');

    try {
      // 重置匹配状态
      _danmakuMatchStatus.value = DanmakuMatchStatus.idle;
      _danmakuMatchResult.value = null;

      // 计算视频文件信息
      final videoInfo = await _getVideoInfo(videoPath);
      if (videoInfo == null) {
        final duration = DateTime.now().difference(startTime);
        _danmakuMatchResult.value = DanmakuMatchResult.failure(
          errorMessage: '无法获取视频信息',
          matchDuration: duration,
          isNetworkVideo: isNetworkVideo,
        );
        _danmakuMatchStatus.value = DanmakuMatchStatus.failed;
        debugPrint('无法获取视频信息');
        return [];
      }

      // 开始匹配
      _danmakuMatchStatus.value = DanmakuMatchStatus.matching;

      // 调用弹弹play API匹配视频
      final episodes = await DanmakuApiUtils.matchVideo(
        fileName: videoInfo['fileName'],
        fileHash: videoInfo['fileHash'],
        fileSize: videoInfo['fileSize'],
        duration: videoInfo['duration'],
      );

      if (episodes.isEmpty) {
        final duration = DateTime.now().difference(startTime);
        _danmakuMatchResult.value = DanmakuMatchResult.failure(
          errorMessage: '未找到匹配的节目',
          matchDuration: duration,
          isNetworkVideo: isNetworkVideo,
          fileSize: videoInfo['fileSize'],
          fileHash: videoInfo['fileHash'],
        );
        _danmakuMatchStatus.value = DanmakuMatchStatus.failed;
        debugPrint('未找到匹配的节目');
        return [];
      }

      // 获取第一个匹配结果的弹幕
      final episode = episodes.first;
      final comments = await DanmakuApiUtils.getComments(episode.episodeId);

      // 匹配成功
      final duration = DateTime.now().difference(startTime);
      _danmakuMatchResult.value = DanmakuMatchResult.success(
        episodeName: '${episode.animeTitle} - ${episode.episodeTitle}',
        confidence: 1.0 - episode.shift, // 使用shift作为置信度的基础
        danmakuCount: comments.length,
        matchDuration: duration,
        isNetworkVideo: isNetworkVideo,
        fileSize: videoInfo['fileSize'],
        fileHash: videoInfo['fileHash'],
      );
      _danmakuMatchStatus.value = DanmakuMatchStatus.success;

      // 转换为内部弹幕格式
      return comments.map((comment) => comment.toDanmaku()).toList();
    } catch (e) {
      final duration = DateTime.now().difference(startTime);
      _danmakuMatchResult.value = DanmakuMatchResult.failure(
        errorMessage: e.toString(),
        matchDuration: duration,
        isNetworkVideo: isNetworkVideo,
      );
      _danmakuMatchStatus.value = DanmakuMatchStatus.failed;
      debugPrint('从API获取弹幕失败: $e');
      return [];
    }
  }

  /// 保存弹幕数据到缓存（优化版本）
  /// 使用智能缓存策略，根据视频类型和弹幕数量决定是否缓存
  Future<void> _saveDanmakusToCache(
    String videoPath,
    List<Danmaku> danmakus,
  ) async {
    try {
      final isNetworkVideo = videoPath.startsWith('http');

      // 使用智能缓存策略
      if (!DanmakuOptimizer.shouldCacheDanmaku(
        videoPath: videoPath,
        danmakuCount: danmakus.length,
        isNetworkVideo: isNetworkVideo,
      )) {
        debugPrint('根据智能缓存策略，跳过缓存: 弹幕数量=${danmakus.length}');
        return;
      }

      // 生成缓存文件路径
      final uniqueKey = CryptoUtils.generateVideoUniqueKey(videoPath);
      final cacheDir = Directory('${Directory.systemTemp.path}/danmaku');
      if (!await cacheDir.exists()) {
        await cacheDir.create(recursive: true);
      }

      final cacheFile = File('${cacheDir.path}/$uniqueKey.json');

      // 添加缓存元数据
      final cacheData = {
        'version': '2.0',
        'videoPath': videoPath,
        'isNetworkVideo': isNetworkVideo,
        'danmakuCount': danmakus.length,
        'cacheTime': DateTime.now().millisecondsSinceEpoch,
        'expireTime':
            DateTime.now()
                .add(
                  DanmakuOptimizer.getCacheExpiration(
                    danmakuCount: danmakus.length,
                    isNetworkVideo: isNetworkVideo,
                  ),
                )
                .millisecondsSinceEpoch,
        'danmakus': danmakus.map((danmaku) => danmaku.toJson()).toList(),
      };

      await cacheFile.writeAsString(jsonEncode(cacheData));
      final historyService = GetIt.I.get<HistoryService>();
      await historyService.saveDanmakuUpdateTime(videoPath: videoPath);

      debugPrint('弹幕缓存保存成功: ${cacheFile.path}, 弹幕数量: ${danmakus.length}');

      // 预加载相关弹幕（异步执行，不阻塞当前流程）
      if (!isNetworkVideo) {
        _preloadRelatedDanmakus(videoPath);
      }
    } catch (e) {
      debugPrint('保存弹幕缓存失败: $e');
    }
  }

  /// 获取视频文件信息（带进度状态）
  Future<Map<String, dynamic>?> _getVideoInfo(String videoPath) async {
    try {
      if (videoPath.startsWith('http://') || videoPath.startsWith('https://')) {
        // 网络视频：尝试获取真实文件hash
        _danmakuMatchStatus.value = DanmakuMatchStatus.downloadingVideo;

        try {
          // 使用异步网络hash计算，避免阻塞主线程
          final hashResult =
              await DanmakuOptimizer.calculateNetworkVideoHashAsync(
                videoPath,
                onProgress: (downloaded, total) {
                  debugPrint('下载进度: $downloaded / ${total ?? "未知"}');
                },
              );

          _danmakuMatchStatus.value = DanmakuMatchStatus.calculatingHash;

          return {
            'fileName': Uri.parse(videoPath).pathSegments.last,
            'fileHash': hashResult.hash,
            'fileSize': hashResult.totalFileSize ?? 0,
            'duration': duration.inSeconds,
          };
        } catch (e) {
          debugPrint('网络视频hash计算失败，回退到URL hash: $e');
          // 回退到URL hash
          return {
            'fileName': Uri.parse(videoPath).pathSegments.last,
            'fileHash': CryptoUtils.calculateNetworkVideoHash(videoPath),
            'fileSize': 0,
            'duration': duration.inSeconds,
          };
        }
      } else {
        // 本地文件：计算实际文件信息
        _danmakuMatchStatus.value = DanmakuMatchStatus.calculatingHash;

        final file = File(videoPath);
        if (!await file.exists()) {
          return null;
        }

        final fileSize = await file.length();
        // 使用异步hash计算，避免阻塞主线程
        final fileHash = await DanmakuOptimizer.calculateVideoHashAsync(
          videoPath,
        );
        final fileName = file.path.split('/').last;

        return {
          'fileName': fileName,
          'fileHash': fileHash,
          'fileSize': fileSize,
          'duration': duration.inSeconds,
        };
      }
    } catch (e) {
      debugPrint('获取视频信息失败: $e');
      return null;
    }
  }

  /// 检查并更新弹幕数据（自动刷新）
  Future<void> refreshDanmakus() async {
    try {
      // 检查缓存是否过期（例如：超过72小时）
      if (history.danmakuUpdateTime > 0) {
        final lastUpdate = DateTime.fromMillisecondsSinceEpoch(
          history.danmakuUpdateTime,
        );
        final now = DateTime.now();
        final difference = now.difference(lastUpdate);

        // 如果缓存时间小于72小时，不需要更新
        if (difference.inHours < 72) {
          debugPrint('弹幕缓存仍然有效，跳过自动更新');
          return;
        }
      }

      // 强制重新加载弹幕
      debugPrint('弹幕缓存已过期，自动重新加载');
      await loadDanmaku(force: true);
    } catch (e) {
      debugPrint('自动刷新弹幕失败: $e');
    }
  }

  /// 强制刷新弹幕（手动）
  Future<void> forceRefreshDanmakus() async {
    debugPrint('开始手动刷新弹幕...');
    // 不在此处显示UI通知，应由UI层负责
    await loadDanmaku(force: true);
  }

  /// 手动匹配并加载弹幕 TODO
  /// 在当前实现中，手动匹配和强制刷新的后端逻辑是一样的，都是重新走一遍API流程
  Future<void> forceMatchAndLoadDanmaku() async {
    // UI上可能会有不同的表现（例如弹窗），但调用的核心服务是同一个
    await forceRefreshDanmakus();
  }

  /// 预加载相关弹幕（异步执行）
  /// 根据当前视频预测可能播放的下一集，提前加载弹幕
  void _preloadRelatedDanmakus(String currentVideoPath) {
    // 异步执行，不阻塞当前流程
    Timer(const Duration(seconds: 5), () async {
      try {
        final predictions = DanmakuOptimizer.predictNextEpisodes(
          currentVideoPath,
        );

        for (final videoPath in predictions) {
          // 检查是否已有缓存
          final cachedDanmakus = await _getCachedDanmakus(videoPath);
          if (cachedDanmakus.isEmpty) {
            debugPrint('预加载弹幕: $videoPath');
            // 这里可以实现预加载逻辑，但要避免影响当前播放
            // 暂时只记录日志，实际实现可以在后台静默加载
          }
        }
      } catch (e) {
        debugPrint('预加载相关弹幕失败: $e');
      }
    });
  }

  /// 更新弹幕设置
  void updateDanmakuSettings(DanmakuSettings settings) {
    _danmakuSettings.value = settings;
  }

  void dispose() {
    _danmakus.dispose();
    _danmakuSettings.dispose();
    _danmakuMatchStatus.dispose();
    _danmakuMatchResult.dispose();
  }
}
