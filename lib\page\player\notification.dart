import 'dart:async';
import 'package:flutter/material.dart';

/// 播放器通知类型
enum PlayerNotificationType {
  info,
  success,
  warning,
  error,
}

/// 播放器通知数据
class PlayerNotification {
  final String message;
  final PlayerNotificationType type;
  final Duration duration;
  final IconData? icon;

  const PlayerNotification({
    required this.message,
    this.type = PlayerNotificationType.info,
    this.duration = const Duration(seconds: 3),
    this.icon,
  });
}

/// 播放器通知组件
/// 在底部控制栏外的左方显示小提示
class PlayerNotificationWidget extends StatefulWidget {
  final PlayerNotification? notification;
  final VoidCallback? onDismiss;

  const PlayerNotificationWidget({
    super.key,
    this.notification,
    this.onDismiss,
  });

  @override
  State<PlayerNotificationWidget> createState() => _PlayerNotificationWidgetState();
}

class _PlayerNotificationWidgetState extends State<PlayerNotificationWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  Timer? _dismissTimer;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(-1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void didUpdateWidget(PlayerNotificationWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.notification != oldWidget.notification) {
      if (widget.notification != null) {
        _showNotification();
      } else {
        _hideNotification();
      }
    }
  }

  @override
  void dispose() {
    _dismissTimer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  void _showNotification() {
    _dismissTimer?.cancel();
    _animationController.forward();
    
    if (widget.notification != null) {
      _dismissTimer = Timer(widget.notification!.duration, () {
        _hideNotification();
      });
    }
  }

  void _hideNotification() {
    _animationController.reverse().then((_) {
      widget.onDismiss?.call();
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.notification == null) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return SlideTransition(
          position: _slideAnimation,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: _buildNotificationCard(),
          ),
        );
      },
    );
  }

  Widget _buildNotificationCard() {
    final notification = widget.notification!;
    
    return Container(
      margin: const EdgeInsets.all(8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: _getBackgroundColor(notification.type),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            notification.icon ?? _getDefaultIcon(notification.type),
            color: Colors.white,
            size: 16,
          ),
          const SizedBox(width: 8),
          Flexible(
            child: Text(
              notification.message,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Color _getBackgroundColor(PlayerNotificationType type) {
    switch (type) {
      case PlayerNotificationType.info:
        return Colors.blue.withValues(alpha: 0.9);
      case PlayerNotificationType.success:
        return Colors.green.withValues(alpha: 0.9);
      case PlayerNotificationType.warning:
        return Colors.orange.withValues(alpha: 0.9);
      case PlayerNotificationType.error:
        return Colors.red.withValues(alpha: 0.9);
    }
  }

  IconData _getDefaultIcon(PlayerNotificationType type) {
    switch (type) {
      case PlayerNotificationType.info:
        return Icons.info_outline;
      case PlayerNotificationType.success:
        return Icons.check_circle_outline;
      case PlayerNotificationType.warning:
        return Icons.warning_amber_outlined;
      case PlayerNotificationType.error:
        return Icons.error_outline;
    }
  }
}

/// 播放器通知管理器
class PlayerNotificationManager {
  static PlayerNotification? _currentNotification;
  static final List<VoidCallback> _listeners = [];

  /// 获取当前通知
  static PlayerNotification? get currentNotification => _currentNotification;

  /// 显示信息通知
  static void showInfo(String message, {Duration? duration, IconData? icon}) {
    _showNotification(PlayerNotification(
      message: message,
      type: PlayerNotificationType.info,
      duration: duration ?? const Duration(seconds: 3),
      icon: icon,
    ));
  }

  /// 显示成功通知
  static void showSuccess(String message, {Duration? duration, IconData? icon}) {
    _showNotification(PlayerNotification(
      message: message,
      type: PlayerNotificationType.success,
      duration: duration ?? const Duration(seconds: 3),
      icon: icon,
    ));
  }

  /// 显示警告通知
  static void showWarning(String message, {Duration? duration, IconData? icon}) {
    _showNotification(PlayerNotification(
      message: message,
      type: PlayerNotificationType.warning,
      duration: duration ?? const Duration(seconds: 3),
      icon: icon,
    ));
  }

  /// 显示错误通知
  static void showError(String message, {Duration? duration, IconData? icon}) {
    _showNotification(PlayerNotification(
      message: message,
      type: PlayerNotificationType.error,
      duration: duration ?? const Duration(seconds: 4),
      icon: icon,
    ));
  }

  /// 显示通知
  static void _showNotification(PlayerNotification notification) {
    _currentNotification = notification;
    _notifyListeners();
  }

  /// 隐藏通知
  static void hideNotification() {
    _currentNotification = null;
    _notifyListeners();
  }

  /// 添加监听器
  static void addListener(VoidCallback listener) {
    _listeners.add(listener);
  }

  /// 移除监听器
  static void removeListener(VoidCallback listener) {
    _listeners.remove(listener);
  }

  /// 通知监听器
  static void _notifyListeners() {
    for (final listener in _listeners) {
      listener();
    }
  }

  /// 释放资源
  static void dispose() {
    _listeners.clear();
    _currentNotification = null;
  }
}
