import 'package:dandanplay_flutter/router.dart';
import 'package:dandanplay_flutter/service/service_locator.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:fvp/fvp.dart' as fvp;
import 'package:signals_flutter/signals_flutter.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // register services
  await ServiceLocator.initialize();
  SignalsObserver.instance = null;
  fvp.registerWith();

  runApp(Application());
}

class Application extends StatefulWidget {
  const Application({super.key});

  @override
  State<Application> createState() => _ApplicationState();
}

class _ApplicationState extends State<Application> with WidgetsBindingObserver {
  _ApplicationState();
  final lightTheme = FThemes.blue.light;
  final darkTheme = FThemes.blue.dark;
  final _isDark = signal(
    WidgetsBinding.instance.platformDispatcher.platformBrightness ==
        Brightness.dark,
  );
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangePlatformBrightness() {
    _isDark.value =
        WidgetsBinding.instance.platformDispatcher.platformBrightness ==
        Brightness.dark;
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      localizationsDelegates: FLocalizations.localizationsDelegates,
      supportedLocales: FLocalizations.supportedLocales,
      theme: lightTheme.toApproximateMaterialTheme(),
      darkTheme: darkTheme.toApproximateMaterialTheme(),
      themeMode: ThemeMode.system,
      builder:
          (context, child) => FToaster(
            child: Watch(
              (context) => FTheme(
                data: _isDark.watch(context) ? darkTheme : lightTheme,
                child: child!,
              ),
            ),
          ),
      routerConfig: router,
    );
  }
}
