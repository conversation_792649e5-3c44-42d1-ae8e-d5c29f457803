import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:signals/signals.dart';

import '../../page/player/indicator.dart';
import '../../page/player/notification.dart';

/// 播放器UI状态管理类
/// 统一管理所有UI相关的状态，使用Signal替代setState
class PlayerUIState {
  // 控制栏显示状态
  final Signal<bool> _showControls = Signal(true);
  Timer? _hideControlsTimer;

  // 手势控制状态
  final Signal<bool> _isGesturing = Signal(false);
  final Signal<bool> _showProgressIndicator = Signal(false);
  final Signal<bool> _showDanmakuSettings = Signal(false);

  // 指示器状态
  final Signal<IndicatorType?> _activeIndicator = Signal(null);
  final Signal<double> _indicatorValue = Signal(0.0);
  Timer? _hideIndicatorTimer;

  // 当前控制值
  final Signal<double> _currentVolume = Signal(0.5);
  final Signal<double> _currentBrightness = Signal(0.5);
  final Signal<String> _progressIndicatorText = Signal("");

  // 手势拖动初始值
  double? _initialVolumeOnPan;
  double? _initialBrightnessOnPan;
  Duration? _initialPositionOnPan;

  // 通知状态
  final Signal<PlayerNotification?> _currentNotification = Signal(null);

  // 只读访问器
  ReadonlySignal<bool> get showControls => _showControls.readonly();
  ReadonlySignal<bool> get isGesturing => _isGesturing.readonly();
  ReadonlySignal<IndicatorType?> get activeIndicator =>
      _activeIndicator.readonly();
  ReadonlySignal<double> get indicatorValue => _indicatorValue.readonly();
  ReadonlySignal<bool> get showProgressIndicator =>
      _showProgressIndicator.readonly();
  ReadonlySignal<bool> get showDanmakuSettings =>
      _showDanmakuSettings.readonly();
  ReadonlySignal<double> get currentVolume => _currentVolume.readonly();
  ReadonlySignal<double> get currentBrightness => _currentBrightness.readonly();
  ReadonlySignal<String> get progressIndicatorText =>
      _progressIndicatorText.readonly();
  ReadonlySignal<PlayerNotification?> get currentNotification =>
      _currentNotification.readonly();

  // 手势拖动初始值访问器
  double? get initialVolumeOnPan => _initialVolumeOnPan;
  double? get initialBrightnessOnPan => _initialBrightnessOnPan;
  Duration? get initialPositionOnPan => _initialPositionOnPan;

  /// 显示控制栏并设置自动隐藏
  void showControlsTemporarily() {
    _showControls.value = true;
    _hideControlsTimer?.cancel();
    _hideControlsTimer = Timer(const Duration(seconds: 3), () {
      if (!_isGesturing.value) {
        _showControls.value = false;
      }
    });
  }

  /// 更新控制栏显示状态
  void updateControlsVisibility(bool show) {
    _showControls.value = show;
  }

  /// 开始手势操作
  void startGesture({
    double? initialVolume,
    double? initialBrightness,
    Duration? initialPosition,
  }) {
    // 记录初始值
    _initialVolumeOnPan = initialVolume;
    _initialBrightnessOnPan = initialBrightness;
    _initialPositionOnPan = initialPosition;

    // 批量更新状态
    batch(() {
      _isGesturing.value = true;
      _showControls.value = false;
    });
  }

  /// 结束手势操作
  void endGesture() {
    batch(() {
      _isGesturing.value = false;
      _showProgressIndicator.value = false;
    });

    // 清除初始值
    _initialVolumeOnPan = null;
    _initialBrightnessOnPan = null;
    _initialPositionOnPan = null;
  }

  /// 开始长按（倍速）
  void startLongPress(double speed) {
    batch(() {
      _isGesturing.value = true;
      _showControls.value = false;
      showIndicator(IndicatorType.speed, speed, permanent: true);
    });
  }

  /// 结束长按（倍速）
  void endLongPress() {
    batch(() {
      _isGesturing.value = false;
      hideIndicator();
    });
  }

  /// 显示音量控制
  void setVolume(double volume) {
    _currentVolume.value = volume;
    showIndicator(IndicatorType.volume, volume);
  }

  /// 显示亮度控制
  void setBrightness(double brightness) {
    _currentBrightness.value = brightness;
    showIndicator(IndicatorType.brightness, brightness);
  }

  /// 显示一个通用的指示器（如音量、亮度、速度）
  void showIndicator(
    IndicatorType type,
    double value, {
    bool permanent = false,
  }) {
    batch(() {
      _activeIndicator.value = type;
      _indicatorValue.value = value;
      _showProgressIndicator.value = false;
    });

    _hideIndicatorTimer?.cancel();
    if (!permanent) {
      _hideIndicatorTimer = Timer(const Duration(seconds: 1), hideIndicator);
    }
  }

  /// 隐藏指示器
  void hideIndicator() {
    _activeIndicator.value = null;
  }

  /// 显示进度指示器
  void setProgressIndicator(String text) {
    batch(() {
      _progressIndicatorText.value = text;
      _showProgressIndicator.value = true;
      hideIndicator();
    });
  }

  /// 隐藏所有控制指示器
  void hideAllIndicators() {
    batch(() {
      hideIndicator();
      _showProgressIndicator.value = false;
    });
  }

  void setDanmakuSettingsPanel(bool show) {
    _showDanmakuSettings.value = show;
  }

  /// 更新通知状态
  void updateNotification(PlayerNotification? notification) {
    _currentNotification.value = notification;
  }

  /// 释放资源
  void dispose() {
    debugPrint('PlayerUIState: 开始释放资源');

    _hideControlsTimer?.cancel();
    _hideControlsTimer = null;
    _hideIndicatorTimer?.cancel();
    _hideIndicatorTimer = null;

    // 释放所有信号
    _showControls.dispose();
    _isGesturing.dispose();
    _showProgressIndicator.dispose();
    _showDanmakuSettings.dispose();
    _activeIndicator.dispose();
    _indicatorValue.dispose();
    _currentVolume.dispose();
    _currentBrightness.dispose();
    _progressIndicatorText.dispose();
    _currentNotification.dispose();

    debugPrint('PlayerUIState: 资源释放完成');
  }
}
